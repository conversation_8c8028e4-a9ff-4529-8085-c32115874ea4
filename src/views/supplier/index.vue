<template>
  <div class="supplier-index-container">
    <!-- Search and Filter Section -->
    <div class="search-filter-section">
      <div class="search-card">
        <!-- Quick Search Bar -->
        <div class="quick-search-bar">
          <div class="search-input-group">
            <a-input-search
              v-model:value="searchForm.keyword"
              placeholder="搜索供应商名称、产品关键词..."
              size="large"
              @search="handleSearch"
              @pressEnter="handleSearch"
              class="main-search-input"
            >
              <template #prefix>
                <i class="fas fa-search"></i>
              </template>
            </a-input-search>
            <a-button
              type="primary"
              size="large"
              @click="toggleAdvancedFilters"
              class="filter-toggle-btn"
            >
              <i class="fas fa-filter"></i>
              高级筛选
              <i :class="showAdvancedFilters ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
            </a-button>
          </div>

          <!-- Quick Filter Tags -->
          <div class="quick-filters">
            <a-tag
              v-for="filter in quickFilters"
              :key="filter.key"
              :color="searchForm.quickFilter === filter.key ? '#f94c30' : 'default'"
              @click="handleQuickFilter(filter.key)"
              class="quick-filter-tag"
            >
              <i :class="filter.icon"></i>
              {{ filter.label }}
            </a-tag>
          </div>
        </div>

        <!-- Advanced Filters Panel -->
        <div v-show="showAdvancedFilters" class="advanced-filters-panel">
          <a-row :gutter="16">
            <!-- Location Filters -->
            <a-col :span="6">
              <a-form-item label="地区筛选">
                <a-cascader
                  v-model:value="searchForm.location"
                  :options="locationOptions"
                  placeholder="选择省份/城市"
                  change-on-select
                  allow-clear
                />
              </a-form-item>
            </a-col>

            <!-- Industry Category -->
            <a-col :span="6">
              <a-form-item label="行业类别">
                <a-select
                  v-model:value="searchForm.industry"
                  placeholder="选择行业类别"
                  allow-clear
                  mode="multiple"
                >
                  <a-select-option v-for="industry in industryOptions" :key="industry.value" :value="industry.value">
                    {{ industry.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <!-- Company Size -->
            <a-col :span="6">
              <a-form-item label="企业规模">
                <a-select
                  v-model:value="searchForm.companySize"
                  placeholder="按注册资本"
                  allow-clear
                >
                  <a-select-option v-for="size in companySizeOptions" :key="size.value" :value="size.value">
                    {{ size.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <!-- Establishment Year -->
            <a-col :span="6">
              <a-form-item label="成立年限">
                <a-range-picker
                  v-model:value="searchForm.establishmentYear"
                  picker="year"
                  placeholder="选择年份范围"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <!-- Product Categories -->
            <a-col :span="8">
              <a-form-item label="产品类别">
                <a-tree-select
                  v-model:value="searchForm.productCategories"
                  :tree-data="productCategoryTree"
                  placeholder="选择产品类别"
                  multiple
                  tree-checkable
                  allow-clear
                  :max-tag-count="2"
                />
              </a-form-item>
            </a-col>

            <!-- Business Type -->
            <a-col :span="8">
              <a-form-item label="经营类型">
                <a-checkbox-group v-model:value="searchForm.businessType">
                  <a-checkbox value="manufacturer">制造商</a-checkbox>
                  <a-checkbox value="trader">贸易商</a-checkbox>
                  <a-checkbox value="service">服务商</a-checkbox>
                </a-checkbox-group>
              </a-form-item>
            </a-col>

            <!-- Supplier Type -->
            <a-col :span="8">
              <a-form-item label="供应商类型">
                <a-radio-group v-model:value="searchForm.supplierType">
                  <a-radio value="">全部</a-radio>
                  <a-radio value="recommended">推荐品牌商</a-radio>
                  <a-radio value="normal">普通供应商</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>

          <!-- Filter Actions -->
          <div class="filter-actions">
            <a-space>
              <a-button type="primary" @click="handleSearch" :loading="loading">
                <i class="fas fa-search"></i>
                搜索
              </a-button>
              <a-button @click="handleResetFilters">
                <i class="fas fa-undo"></i>
                重置
              </a-button>
              <a-button type="link" @click="handleSaveFilter">
                <i class="fas fa-save"></i>
                保存筛选条件
              </a-button>
            </a-space>
          </div>
        </div>
      </div>
    </div>

    <!-- Table Operations -->
    <div class="table-operations">
      <a-space>
        <a-button
          type="primary"
          @click="handleExportSuppliers"
          :disabled="selectedSuppliers.length === 0"
        >
          <i class="fas fa-download"></i>
          导出
          <span v-if="selectedSuppliers.length > 0">({{ selectedSuppliers.length }})</span>
        </a-button>
        <a-button
          @click="handleStartComparison"
          :disabled="selectedSuppliers.length < 2 || selectedSuppliers.length > 3"
        >
          <i class="fas fa-balance-scale"></i>
          对比
          <span v-if="selectedSuppliers.length > 0">({{ selectedSuppliers.length }})</span>
        </a-button>
      </a-space>

      <a-space>
        <!-- View Mode Toggle -->
        <a-radio-group v-model:value="viewMode" button-style="solid" size="small">
          <a-radio-button value="list">
            <i class="fas fa-list"></i>
            列表
          </a-radio-button>
          <a-radio-button value="card">
            <i class="fas fa-th-large"></i>
            卡片
          </a-radio-button>
          <a-radio-button value="map">
            <i class="fas fa-map"></i>
            地图
          </a-radio-button>
        </a-radio-group>

      </a-space>
    </div>

    <!-- Selection Summary -->
    <div class="selection-summary">
      <div>
        <a-tooltip placement="top">
          <template #title>
            <div>1. 供应商筛选基于企业工商信息和平台审核状态。</div>
            <div>2. 严选供应商已通过平台审核验证，具备更高可信度。</div>
            <div>3. 发起询价将触发供应商审核流程。</div>
          </template>
          <span style="color: #666"><info-circle-filled style="margin-right: 4px" />供应商说明</span>
        </a-tooltip>
      </div>
      <div class="summary-content">
        <span>找到：<a-tag color="blue">{{ pagination.total }}</a-tag> 家供应商</span>
        <span v-if="selectedSuppliers.length > 0">已选择：<a-tag color="red">{{ selectedSuppliers.length }}</a-tag> 家供应商</span>
        <span v-if="activeFiltersCount > 0">筛选条件：<a-tag color="orange">{{ activeFiltersCount }}</a-tag> 个</span>
      </div>
    </div>

    <!-- Active Filters Display -->
    <div class="active-filters" v-if="activeFilters.length > 0">
      <span class="active-filters-label">当前筛选：</span>
      <a-tag
        v-for="filter in activeFilters"
        :key="filter.key"
        closable
        @close="handleRemoveFilter(filter.key)"
        color="#f94c30"
      >
        {{ filter.label }}
      </a-tag>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
      <!-- Right Content Area -->
      <div class="right-content">
        <!-- List View -->
        <div v-if="viewMode === 'list'" class="list-view">
          <a-table
            :columns="tableColumns"
            :data-source="supplierList"
            :pagination="tablePagination"
            :loading="loading"
            :row-selection="rowSelection"
            row-key="id"
            @change="handleTableChange"
            class="supplier-table"
            size="middle"
            bordered
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <div class="supplier-name-cell">
                  <a @click="handleViewSupplierDetail(record)" style="color: #1890ff;">{{ record.companyName }}</a>
                  <a-tag v-if="record.supplierType === 'recommended'" color="gold">
                    <i class="fas fa-crown"></i>
                    推荐品牌商
                  </a-tag>
                  <i v-if="record.isBookmarked" class="fas fa-heart bookmark-icon" title="已收藏"></i>
                </div>
              </template>

              <template v-if="column.key === 'location'">
                {{ record.region }}
              </template>

              <template v-if="column.key === 'registeredCapital'">
                {{ record.registeredCapital }}
              </template>

              <template v-if="column.key === 'establishmentYear'">
                {{ formatTimestamp(record.establishmentTime) }}年
              </template>

              <template v-if="column.key === 'actions'">
                <a-space>
                  <a-button
                    type="primary"
                    size="small"
                    @click="handleInitiateInquiry(record)"
                    :disabled="record.supplierType !== 'recommended'"
                  >
                    发起询价
                  </a-button>
                  <a-dropdown>
                    <a-button size="small">
                      更多
                      <i class="fas fa-caret-down"></i>
                    </a-button>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="handleAddToComparison(record)">
                          <i class="fas fa-balance-scale"></i>
                          对比
                        </a-menu-item>
                        <a-menu-item @click="handleViewSupplierDetail(record)">
                          <i class="fas fa-eye"></i>
                          详情
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>

        <!-- Card View -->
        <div v-else-if="viewMode === 'card'" class="card-view">
          <a-row :gutter="[16, 16]">
            <a-col
              v-for="supplier in supplierList"
              :key="supplier.id"
              :xs="24"
              :sm="12"
              :md="8"
              :lg="6"
            >
              <a-card
                class="supplier-card"
                :class="{ 'selected': selectedSuppliers.includes(supplier.id) }"
                @click="handleSelectSupplier(supplier.id)"
              >
                <template #title>
                  <div class="card-title">
                    <a-checkbox
                      :checked="selectedSuppliers.includes(supplier.id)"
                      @click.stop="handleSelectSupplier(supplier.id)"
                    />
                    <span class="supplier-name">{{ supplier.companyName }}</span>
                    <div class="title-actions">
                      <a-button
                        type="text"
                        size="small"
                        @click.stop="handleToggleBookmark(supplier)"
                        class="bookmark-btn"
                        :class="{ 'bookmarked': supplier.isBookmarked }"
                      >
                        <i :class="supplier.isBookmarked ? 'fas fa-heart' : 'far fa-heart'"></i>
                      </a-button>
                    </div>
                  </div>
                </template>

                <template #extra>
                  <a-dropdown>
                    <a-button type="text" size="small">
                      <i class="fas fa-ellipsis-v"></i>
                    </a-button>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="handleAddToComparison(supplier)">
                          <i class="fas fa-balance-scale"></i>
                          对比
                        </a-menu-item>
                        <a-menu-item @click="handleViewSupplierDetail(supplier)">
                          <i class="fas fa-eye"></i>
                          详情
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </template>

                <div class="card-content">
                  <!-- Logo和基本信息区域 -->
                  <div class="supplier-header">
                    <div class="supplier-logo">
                      <img
                        v-if="supplier.logoUrl"
                        :src="supplier.logoUrl"
                        :alt="supplier.companyName"
                        @error="handleLogoError"
                      />
                      <div v-else class="default-logo">
                        <i class="fas fa-building"></i>
                      </div>
                    </div>
                    <div class="supplier-basic-info">
                      <div class="supplier-tags">
                        <a-tag
                          v-if="supplier.supplierType === 'recommended'"
                          color="gold"
                          class="recommended-tag"
                        >
                          <i class="fas fa-crown"></i>
                          推荐品牌商
                        </a-tag>
                        <a-tag color="blue" size="small">{{ supplier.factoryLevel }}</a-tag>
                      </div>
                      <div class="supplier-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>{{ supplier.region }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- 详细信息区域 -->
                  <div class="supplier-details">
                    <div class="detail-row">
                      <span class="detail-label">行业类别</span>
                      <span class="detail-value">{{ supplier.industry }}</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">成立时间</span>
                      <span class="detail-value">{{ formatTimestamp(supplier.establishmentTime) }}年</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">注册资本</span>
                      <span class="detail-value">{{ supplier.registeredCapital }}</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">员工规模</span>
                      <span class="detail-value">{{ supplier.socialSecurityCount }}人</span>
                    </div>
                  </div>

                  <!-- 产品关键词 -->
                  <div class="supplier-products">
                    <div class="products-label">主营产品</div>
                    <div class="products-tags">
                      <a-tag
                        v-for="keyword in JSON.parse(supplier.searchKeywords).slice(0, 3)"
                        :key="keyword"
                        size="small"
                        color="blue"
                      >
                        {{ keyword }}
                      </a-tag>
                      <span v-if="JSON.parse(supplier.searchKeywords).length > 3" class="more-products">
                        +{{ JSON.parse(supplier.searchKeywords).length - 3 }}
                      </span>
                    </div>
                  </div>
                </div>

                <template #actions>
                  <a-button
                    type="primary"
                    block
                    @click.stop="handleInitiateInquiry(supplier)"
                    :disabled="supplier.supplierType !== 'recommended'"
                  >
                    <i class="fas fa-paper-plane"></i>
                    发起询价
                  </a-button>
                </template>
              </a-card>
            </a-col>
          </a-row>

          <!-- Card View Pagination -->
          <div class="card-pagination">
            <a-pagination
              v-model:current="pagination.current"
              v-model:page-size="pagination.pageSize"
              :total="pagination.total"
              :show-size-changer="true"
              :show-quick-jumper="true"
              :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
              @change="handlePaginationChange"
            />
          </div>
        </div>

        <!-- Map View -->
        <div v-else-if="viewMode === 'map'" class="map-view">
          <div class="map-container">
            <div id="supplier-map" class="map-canvas"></div>

            <!-- Map Controls -->
            <div class="map-controls">
              <a-card size="small" class="map-legend">
                <div class="legend-item">
                  <span class="legend-dot recommended"></span>
                  <span>推荐品牌商</span>
                </div>
                <div class="legend-item">
                  <span class="legend-dot normal"></span>
                  <span>普通供应商</span>
                </div>
              </a-card>

              <a-card size="small" class="map-stats">
                <a-statistic title="当前区域供应商" :value="mapStats.currentRegion" />
                <a-statistic title="推荐品牌商" :value="mapStats.recommended" />
                <a-statistic title="普通供应商" :value="mapStats.normal" />
              </a-card>
            </div>

            <!-- Map Supplier Info Panel -->
            <div v-if="selectedMapSupplier" class="map-supplier-panel">
              <a-card size="small">
                <template #title>
                  {{ selectedMapSupplier.name }}
                  <a-button
                    type="text"
                    size="small"
                    @click="selectedMapSupplier = null"
                    style="float: right"
                  >
                    <i class="fas fa-times"></i>
                  </a-button>
                </template>

                <div class="map-supplier-info">
                  <p><strong>地址：</strong>{{ selectedMapSupplier.address }}</p>
                  <p><strong>行业：</strong>{{ selectedMapSupplier.industry }}</p>
                  <p><strong>成立时间：</strong>{{ selectedMapSupplier.establishmentYear }}年</p>
                  <p><strong>注册资本：</strong>{{ formatCurrency(selectedMapSupplier.registeredCapital) }}</p>
                </div>

                <template #actions>
                  <a-button type="primary" size="small" @click="handleInitiateInquiry(selectedMapSupplier)">
                    发起询价
                  </a-button>
                  <a-button size="small" @click="handleViewSupplierDetail(selectedMapSupplier)">
                    查看详情
                  </a-button>
                </template>
              </a-card>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <!-- Supplier Detail Modal -->
    <a-modal
      v-model:open="showSupplierDetailModal"
      title="供应商详情"
      width="800px"
      :footer="null"
      class="supplier-detail-modal"
    >
      <div v-if="selectedSupplierDetail" class="supplier-detail-content">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="企业名称">{{ selectedSupplierDetail.companyName }}</a-descriptions-item>
          <a-descriptions-item label="供应商类型">
            <a-tag v-if="selectedSupplierDetail.supplierType === 'recommended'" color="gold">
              <i class="fas fa-crown"></i>
              推荐品牌商
            </a-tag>
            <span v-else>普通供应商</span>
          </a-descriptions-item>
          <a-descriptions-item label="卖家昵称">{{ selectedSupplierDetail.sellerNickname }}</a-descriptions-item>
          <a-descriptions-item label="店铺名称">{{ selectedSupplierDetail.shopName }}</a-descriptions-item>
          <a-descriptions-item label="所在地区">{{ selectedSupplierDetail.region }}</a-descriptions-item>
          <a-descriptions-item label="行业类别">{{ selectedSupplierDetail.industry }}</a-descriptions-item>
          <a-descriptions-item label="工厂等级">{{ selectedSupplierDetail.factoryLevel }}</a-descriptions-item>
          <a-descriptions-item label="等级分数">{{ selectedSupplierDetail.levelScore }}分</a-descriptions-item>
          <a-descriptions-item label="成立时间">{{ formatTimestamp(selectedSupplierDetail.establishmentTime) }}年</a-descriptions-item>
          <a-descriptions-item label="注册资本">{{ selectedSupplierDetail.registeredCapital }}</a-descriptions-item>
          <a-descriptions-item label="社保人数">{{ selectedSupplierDetail.socialSecurityCount }}人</a-descriptions-item>
          <a-descriptions-item label="服务年限">{{ selectedSupplierDetail.serviceYears }}年</a-descriptions-item>
          <a-descriptions-item label="联系电话">{{ selectedSupplierDetail.phone }}</a-descriptions-item>
          <a-descriptions-item label="企业邮箱">{{ selectedSupplierDetail.email }}</a-descriptions-item>
          <a-descriptions-item label="企业网站">
            <a :href="selectedSupplierDetail.website" target="_blank">{{ selectedSupplierDetail.website }}</a>
          </a-descriptions-item>
          <a-descriptions-item label="法定代表人">{{ selectedSupplierDetail.legalRepresentative }}</a-descriptions-item>
          <a-descriptions-item label="注册地址" :span="2">{{ selectedSupplierDetail.registeredAddress }}</a-descriptions-item>
          <a-descriptions-item label="经营范围" :span="2">{{ selectedSupplierDetail.businessScope }}</a-descriptions-item>
          <a-descriptions-item label="搜索关键词" :span="2">
            <a-tag v-for="keyword in JSON.parse(selectedSupplierDetail.searchKeywords)" :key="keyword">
              {{ keyword }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="公司标签" :span="2">
            <a-tag v-for="tag in JSON.parse(selectedSupplierDetail.companyTags)" :key="tag" color="blue">
              {{ tag }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>

        <div class="detail-actions">
          <a-space>
            <a-button type="primary" @click="handleInitiateInquiry(selectedSupplierDetail)">
              <i class="fas fa-paper-plane"></i>
              发起询价
            </a-button>
            <a-button @click="handleAddToComparison(selectedSupplierDetail)">
              <i class="fas fa-balance-scale"></i>
              对比
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- Inquiry Modal -->
    <a-modal
      v-model:open="showInquiryModal"
      title="发起询价"
      width="600px"
      :confirm-loading="inquiryLoading"
      @ok="handleSubmitInquiry"
      @cancel="handleCancelInquiry"
      class="inquiry-modal"
    >
      <a-form :model="inquiryForm" layout="vertical">
        <a-form-item label="选择供应商" required>
          <a-input :value="inquiryForm.supplierName" disabled />
        </a-form-item>

        <a-form-item label="零件规格" required>
          <a-textarea
            v-model:value="inquiryForm.partSpecifications"
            placeholder="请详细描述零件规格、型号、技术要求等"
            :rows="3"
          />
        </a-form-item>

        <a-form-item label="采购数量" required>
          <a-input-number
            v-model:value="inquiryForm.quantity"
            placeholder="请输入采购数量"
            :min="1"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="交付要求">
          <a-textarea
            v-model:value="inquiryForm.deliveryRequirements"
            placeholder="请描述交付时间、地点、包装等要求"
            :rows="2"
          />
        </a-form-item>

        <a-form-item label="附件上传">
          <a-upload
            v-model:file-list="inquiryForm.attachments"
            :before-upload="() => false"
            multiple
          >
            <a-button>
              <i class="fas fa-upload"></i>
              上传文件
            </a-button>
          </a-upload>
        </a-form-item>

        <a-form-item label="备注">
          <a-textarea
            v-model:value="inquiryForm.remark"
            placeholder="其他说明或特殊要求"
            :rows="2"
          />
        </a-form-item>
      </a-form>

      <a-alert
        message="询价提醒"
        description="发起询价将触发我们的运营团队对该供应商进行审核，审核通过后供应商才能提供正式报价。"
        type="info"
        show-icon
        style="margin-top: 16px"
      />
    </a-modal>

    <!-- Inquiry Tracking Modal -->
    <a-modal
      v-model:open="showInquiryTrackingModal"
      title="我的询价跟踪"
      width="900px"
      :footer="null"
      class="inquiry-tracking-modal"
    >
      <a-table
        :columns="inquiryTrackingColumns"
        :data-source="inquiryTrackingList"
        :pagination="false"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getInquiryStatusColor(record.status)">
              {{ getInquiryStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small">查看详情</a-button>
              <a-button type="link" size="small" v-if="record.status === 'quoted'">查看报价</a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-modal>



    <!-- Comparison Modal -->
    <a-modal
      v-model:open="showComparisonModal"
      title="供应商对比分析"
      width="1000px"
      :footer="null"
      class="comparison-modal"
    >
      <div class="comparison-content">
        <a-table
          :columns="comparisonColumns"
          :data-source="comparisonData"
          :pagination="false"
          bordered
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import { InfoCircleFilled } from '@ant-design/icons-vue'

// ==================== Reactive Data ====================

// Loading states
const loading = ref(false)
const inquiryLoading = ref(false)

// View mode
const viewMode = ref('card') // 'list', 'card', 'map'
const sortBy = ref('relevance')

// Search and filter form
const searchForm = reactive({
  keyword: '',
  location: [],
  industry: [],
  companySize: '',
  establishmentYear: [],
  productCategories: [],
  businessType: [],
  supplierType: '',
  quickFilter: ''
})

// UI states
const showAdvancedFilters = ref(false)
const showSupplierDetailModal = ref(false)
const showInquiryModal = ref(false)
const showInquiryTrackingModal = ref(false)
const showComparisonModal = ref(false)

// Selection states
const selectedSuppliers = ref([])
const selectedSupplierDetail = ref(null)
const selectedMapSupplier = ref(null)

// Pagination
const pagination = reactive({
  current: 1,
  pageSize: 12,
  total: 0
})

// Table pagination (for list view)
const tablePagination = computed(() => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
}))

// Quick filters
const quickFilters = ref([
  { key: 'all', label: '全部', icon: 'fas fa-list' },
  { key: 'recommended', label: '推荐品牌商', icon: 'fas fa-crown' },
  { key: 'mechanical', label: '机械零件', icon: 'fas fa-cog' },
  { key: 'electrical', label: '电气元件', icon: 'fas fa-bolt' },
  { key: 'hydraulic', label: '液压组件', icon: 'fas fa-tint' }
])

// Filter options
const locationOptions = ref([
  {
    value: 'guangdong',
    label: '广东省',
    children: [
      { value: 'shenzhen', label: '深圳市' },
      { value: 'guangzhou', label: '广州市' },
      { value: 'dongguan', label: '东莞市' }
    ]
  },
  {
    value: 'jiangsu',
    label: '江苏省',
    children: [
      { value: 'suzhou', label: '苏州市' },
      { value: 'nanjing', label: '南京市' },
      { value: 'wuxi', label: '无锡市' }
    ]
  },
  {
    value: 'zhejiang',
    label: '浙江省',
    children: [
      { value: 'hangzhou', label: '杭州市' },
      { value: 'ningbo', label: '宁波市' },
      { value: 'wenzhou', label: '温州市' }
    ]
  }
])

const industryOptions = ref([
  { value: 'mechanical', label: '机械制造' },
  { value: 'electronics', label: '电子电气' },
  { value: 'automation', label: '自动化设备' },
  { value: 'precision', label: '精密加工' },
  { value: 'materials', label: '新材料' },
  { value: 'hydraulic', label: '液压气动' }
])

const companySizeOptions = ref([
  { value: 'small', label: '小型企业 (< 500万)' },
  { value: 'medium', label: '中型企业 (500万-5000万)' },
  { value: 'large', label: '大型企业 (> 5000万)' }
])

const productCategoryTree = ref([
  {
    title: '机械零件',
    value: 'mechanical',
    key: 'mechanical',
    children: [
      { title: '轴承', value: 'bearing', key: 'bearing' },
      { title: '齿轮', value: 'gear', key: 'gear' },
      { title: '联轴器', value: 'coupling', key: 'coupling' }
    ]
  },
  {
    title: '电气元件',
    value: 'electrical',
    key: 'electrical',
    children: [
      { title: '传感器', value: 'sensor', key: 'sensor' },
      { title: '电机', value: 'motor', key: 'motor' },
      { title: '控制器', value: 'controller', key: 'controller' }
    ]
  },
  {
    title: '液压组件',
    value: 'hydraulic',
    key: 'hydraulic',
    children: [
      { title: '液压泵', value: 'pump', key: 'pump' },
      { title: '液压缸', value: 'cylinder', key: 'cylinder' },
      { title: '液压阀', value: 'valve', key: 'valve' }
    ]
  }
])

// Supplier data
const supplierList = ref([])

// Mock supplier data - 更新为新的字段结构
const mockSupplierData = [
  {
    id: '1',
    supplierId: 'SUP001',
    companyName: '深圳精密机械有限公司',
    sellerId: 'SELLER001',
    sellerNickname: '精密机械专家',
    shopName: '深圳精密机械旗舰店',
    region: '广东省深圳市',
    imageUrl: 'https://example.com/supplier1.jpg',
    logoUrl: 'https://via.placeholder.com/80x80/1890ff/ffffff?text=深圳精密', // 公司logo
    factoryLevel: 'A级',
    levelScore: 95,
    serviceYears: 8,
    wangwangResponseScore: 92,
    goodRatingScore: 98,
    complianceScore: 96,
    buyerIntention: '高',
    categoryName: '机械零件',
    homepageLink: 'https://shop.example.com/supplier1',
    searchKeywords: JSON.stringify(['精密轴承', '齿轮箱', '联轴器']),
    tianyanchaCompanyId: 'TYC001',
    tianyanchaCompanyName: '深圳精密机械有限公司',
    registrationNumber: '91440300123456789X',
    registrationStatus: '存续',
    unifiedSocialCreditCode: '91440300123456789X',
    establishmentTime: 1420070400000, // 2015-01-01的时间戳
    registeredCapital: '500万人民币',
    companyType: '有限责任公司',
    organizationCode: '12345678-9',
    legalRepresentative: '张三',
    businessScope: '精密机械零件制造、销售；机械设备技术开发',
    registeredAddress: '深圳市南山区科技园南区',
    phone: '0755-12345678',
    email: '<EMAIL>',
    emailList: JSON.stringify(['<EMAIL>', '<EMAIL>']),
    website: 'https://www.szjmjx.com',
    approvalDate: 1420070400000,
    businessTermFrom: 1420070400000,
    businessTermTo: 1735660800000, // 2025-01-01
    socialSecurityCount: 120,
    formerName: '',
    industry: '机械制造',
    stockName: '',
    stockCode: '',
    formerStockName: '',
    taxpayerIdentificationNumber: '91440300123456789X',
    companyAbbreviation: '深圳精密',
    district: '南山区',
    companyTags: JSON.stringify(['精密制造', '高新技术', '质量认证']),
    supplierType: 'recommended', // 推荐品牌商
    isBookmarked: true // 收藏状态
  },
  {
    id: '2',
    supplierId: 'SUP002',
    companyName: '苏州电子科技股份有限公司',
    sellerId: 'SELLER002',
    sellerNickname: '电子科技先锋',
    shopName: '苏州电子科技专营店',
    region: '江苏省苏州市',
    imageUrl: 'https://example.com/supplier2.jpg',
    logoUrl: 'https://via.placeholder.com/80x80/52c41a/ffffff?text=苏州电子', // 公司logo
    factoryLevel: 'B级',
    levelScore: 88,
    serviceYears: 11,
    wangwangResponseScore: 89,
    goodRatingScore: 94,
    complianceScore: 91,
    buyerIntention: '中',
    categoryName: '电子元件',
    homepageLink: 'https://shop.example.com/supplier2',
    searchKeywords: JSON.stringify(['传感器', '控制模块', '电机驱动器']),
    tianyanchaCompanyId: 'TYC002',
    tianyanchaCompanyName: '苏州电子科技股份有限公司',
    registrationNumber: '91320500987654321A',
    registrationStatus: '存续',
    unifiedSocialCreditCode: '91320500987654321A',
    establishmentTime: 1325376000000, // 2012-01-01
    registeredCapital: '800万人民币',
    companyType: '股份有限公司',
    organizationCode: '98765432-1',
    legalRepresentative: '李四',
    businessScope: '电子产品研发、制造、销售；自动化设备技术服务',
    registeredAddress: '苏州市工业园区星海街',
    phone: '0512-87654321',
    email: '<EMAIL>',
    emailList: JSON.stringify(['<EMAIL>', '<EMAIL>']),
    website: 'https://www.szdzkj.com',
    approvalDate: 1325376000000,
    businessTermFrom: 1325376000000,
    businessTermTo: 1735660800000,
    socialSecurityCount: 200,
    formerName: '苏州电子有限公司',
    industry: '电子电气',
    stockName: '',
    stockCode: '',
    formerStockName: '',
    taxpayerIdentificationNumber: '91320500987654321A',
    companyAbbreviation: '苏州电子',
    district: '工业园区',
    companyTags: JSON.stringify(['电子制造', '技术创新', 'ISO认证']),
    supplierType: 'normal', // 普通供应商
    isBookmarked: false
  },
  {
    id: '3',
    supplierId: 'SUP003',
    companyName: '上海智能制造有限公司',
    sellerId: 'SELLER003',
    sellerNickname: '智能制造专家',
    shopName: '上海智能制造旗舰店',
    region: '上海市浦东新区',
    imageUrl: 'https://example.com/supplier3.jpg',
    logoUrl: '', // 没有logo，将显示默认头像
    factoryLevel: 'A级',
    levelScore: 92,
    serviceYears: 6,
    wangwangResponseScore: 95,
    goodRatingScore: 96,
    complianceScore: 94,
    buyerIntention: '高',
    categoryName: '自动化设备',
    homepageLink: 'https://shop.example.com/supplier3',
    searchKeywords: JSON.stringify(['工业机器人', '自动化产线', '智能控制系统']),
    tianyanchaCompanyId: 'TYC003',
    tianyanchaCompanyName: '上海智能制造有限公司',
    registrationNumber: '91310115123456789Y',
    registrationStatus: '存续',
    unifiedSocialCreditCode: '91310115123456789Y',
    establishmentTime: 1483200000000, // 2017-01-01
    registeredCapital: '1200万人民币',
    companyType: '有限责任公司',
    organizationCode: '12345678-0',
    legalRepresentative: '王五',
    businessScope: '智能制造设备研发、生产、销售；工业自动化技术服务',
    registeredAddress: '上海市浦东新区张江高科技园区',
    phone: '021-12345678',
    email: '<EMAIL>',
    emailList: JSON.stringify(['<EMAIL>', '<EMAIL>']),
    website: 'https://www.shznzz.com',
    approvalDate: 1483200000000,
    businessTermFrom: 1483200000000,
    businessTermTo: 1735660800000,
    socialSecurityCount: 180,
    formerName: '',
    industry: '自动化设备',
    stockName: '',
    stockCode: '',
    formerStockName: '',
    taxpayerIdentificationNumber: '91310115123456789Y',
    companyAbbreviation: '上海智能',
    district: '浦东新区',
    companyTags: JSON.stringify(['智能制造', '工业4.0', '技术领先']),
    supplierType: 'recommended', // 推荐品牌商
    isBookmarked: true
  }
]

// Comparison
const comparisonList = ref([])

// Forms
const inquiryForm = reactive({
  supplierId: '',
  supplierName: '',
  partSpecifications: '',
  quantity: null,
  deliveryRequirements: '',
  attachments: [],
  remark: ''
})

// Inquiry tracking
const inquiryTrackingList = ref([
  {
    id: '1',
    inquiryNo: 'INQ-2023-001',
    supplierName: '深圳精密机械有限公司',
    partName: '精密轴承',
    quantity: 100,
    status: 'auditing',
    createTime: '2023-10-15 10:30:00',
    updateTime: '2023-10-16 14:20:00'
  },
  {
    id: '2',
    inquiryNo: 'INQ-2023-002',
    supplierName: '苏州电子科技股份有限公司',
    partName: '工业传感器',
    quantity: 50,
    status: 'quoted',
    createTime: '2023-10-14 09:15:00',
    updateTime: '2023-10-17 16:45:00'
  }
])

// Map stats
const mapStats = reactive({
  currentRegion: 0,
  recommended: 0,
  normal: 0
})

// ==================== Computed Properties ====================

// Active filters count
const activeFiltersCount = computed(() => {
  let count = 0
  if (searchForm.keyword) count++
  if (searchForm.location.length > 0) count++
  if (searchForm.industry.length > 0) count++
  if (searchForm.companySize) count++
  if (searchForm.establishmentYear.length > 0) count++
  if (searchForm.productCategories.length > 0) count++
  if (searchForm.businessType.length > 0) count++
  if (searchForm.auditStatus) count++
  return count
})

// Active filters display
const activeFilters = computed(() => {
  const filters = []

  if (searchForm.keyword) {
    filters.push({ key: 'keyword', label: `关键词: ${searchForm.keyword}` })
  }

  if (searchForm.location.length > 0) {
    const locationLabels = searchForm.location.map(loc => {
      // Find location label from options
      const province = locationOptions.value.find(p => p.value === loc[0])
      if (province && loc[1]) {
        const city = province.children.find(c => c.value === loc[1])
        return city ? `${province.label} ${city.label}` : province.label
      }
      return province ? province.label : loc
    })
    filters.push({ key: 'location', label: `地区: ${locationLabels.join(', ')}` })
  }

  if (searchForm.industry.length > 0) {
    const industryLabels = searchForm.industry.map(ind => {
      const industry = industryOptions.value.find(i => i.value === ind)
      return industry ? industry.label : ind
    })
    filters.push({ key: 'industry', label: `行业: ${industryLabels.join(', ')}` })
  }

  if (searchForm.companySize) {
    const sizeOption = companySizeOptions.value.find(s => s.value === searchForm.companySize)
    filters.push({ key: 'companySize', label: `规模: ${sizeOption ? sizeOption.label : searchForm.companySize}` })
  }

  if (searchForm.supplierType) {
    const typeLabel = searchForm.supplierType === 'recommended' ? '推荐品牌商' : '普通供应商'
    filters.push({ key: 'supplierType', label: `类型: ${typeLabel}` })
  }

  return filters
})

// Row selection for table
const rowSelection = computed(() => ({
  selectedRowKeys: selectedSuppliers.value,
  onChange: (selectedRowKeys) => {
    selectedSuppliers.value = selectedRowKeys
  }
}))

// Table columns
const tableColumns = ref([
  {
    title: '供应商名称',
    dataIndex: 'companyName',
    key: 'name',
    width: 200,
    fixed: 'left'
  },
  {
    title: '所在地区',
    key: 'location',
    width: 120
  },
  {
    title: '行业类别',
    dataIndex: 'industry',
    key: 'industry',
    width: 100
  },
  {
    title: '工厂等级',
    dataIndex: 'factoryLevel',
    key: 'factoryLevel',
    width: 100
  },
  {
    title: '成立时间',
    dataIndex: 'establishmentTime',
    key: 'establishmentYear',
    width: 100,
    sorter: true
  },
  {
    title: '注册资本',
    key: 'registeredCapital',
    width: 120,
    sorter: true
  },
  {
    title: '社保人数',
    dataIndex: 'socialSecurityCount',
    key: 'employeeCount',
    width: 100,
    sorter: true
  },
  {
    title: '服务年限',
    dataIndex: 'serviceYears',
    key: 'serviceYears',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right'
  }
])

// Inquiry tracking columns
const inquiryTrackingColumns = ref([
  {
    title: '询价单号',
    dataIndex: 'inquiryNo',
    key: 'inquiryNo'
  },
  {
    title: '供应商',
    dataIndex: 'supplierName',
    key: 'supplierName'
  },
  {
    title: '零件名称',
    dataIndex: 'partName',
    key: 'partName'
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    key: 'quantity'
  },
  {
    title: '状态',
    key: 'status'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime'
  },
  {
    title: '操作',
    key: 'actions'
  }
])

// Comparison columns
const comparisonColumns = ref([])
const comparisonData = ref([])

// ==================== Methods ====================

// Toggle advanced filters
const toggleAdvancedFilters = () => {
  showAdvancedFilters.value = !showAdvancedFilters.value
}

// Handle search
const handleSearch = async () => {
  loading.value = true
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500))

    // Filter mock data based on search criteria
    let filteredData = [...mockSupplierData]

    // Keyword search
    if (searchForm.keyword) {
      const keyword = searchForm.keyword.toLowerCase()
      filteredData = filteredData.filter(supplier =>
        supplier.companyName.toLowerCase().includes(keyword) ||
        supplier.sellerNickname.toLowerCase().includes(keyword) ||
        supplier.shopName.toLowerCase().includes(keyword) ||
        JSON.parse(supplier.searchKeywords).some(keyword_item => keyword_item.toLowerCase().includes(keyword))
      )
    }

    // Location filter
    if (searchForm.location.length > 0) {
      filteredData = filteredData.filter(supplier => {
        return searchForm.location.some(loc => {
          if (loc.length === 1) {
            return supplier.region.includes(loc[0])
          } else {
            return supplier.region.includes(loc[0]) && supplier.region.includes(loc[1])
          }
        })
      })
    }

    // Industry filter
    if (searchForm.industry.length > 0) {
      filteredData = filteredData.filter(supplier =>
        searchForm.industry.includes(supplier.industry)
      )
    }

    // Supplier type filter
    if (searchForm.supplierType) {
      filteredData = filteredData.filter(supplier =>
        supplier.supplierType === searchForm.supplierType
      )
    }

    // Business type filter
    if (searchForm.businessType.length > 0) {
      filteredData = filteredData.filter(supplier =>
        searchForm.businessType.includes(supplier.businessType)
      )
    }

    // Quick filter
    if (searchForm.quickFilter) {
      switch (searchForm.quickFilter) {
        case 'recommended':
          filteredData = filteredData.filter(s => s.supplierType === 'recommended')
          break
        case 'mechanical':
          filteredData = filteredData.filter(s => s.industry === '机械制造')
          break
        case 'electrical':
          filteredData = filteredData.filter(s => s.industry === '电子电气')
          break
        case 'hydraulic':
          filteredData = filteredData.filter(s => s.industry === '液压气动')
          break
      }
    }

    // Sort data
    if (sortBy.value !== 'relevance') {
      filteredData.sort((a, b) => {
        switch (sortBy.value) {
          case 'establishmentYear':
            return b.establishmentTime - a.establishmentTime
          case 'registeredCapital':
            // 解析注册资本字符串进行比较
            const getCapitalValue = (capital) => {
              const match = capital.match(/(\d+)/);
              return match ? parseInt(match[1]) : 0;
            }
            return getCapitalValue(b.registeredCapital) - getCapitalValue(a.registeredCapital)
          case 'employeeCount':
            return b.socialSecurityCount - a.socialSecurityCount
          default:
            return 0
        }
      })
    }

    // Update pagination
    pagination.total = filteredData.length
    const startIndex = (pagination.current - 1) * pagination.pageSize
    const endIndex = startIndex + pagination.pageSize

    supplierList.value = filteredData.slice(startIndex, endIndex)

  } catch (error) {
    message.error('搜索失败，请重试')
  } finally {
    loading.value = false
  }
}

// Handle quick filter
const handleQuickFilter = (filterKey) => {
  searchForm.quickFilter = searchForm.quickFilter === filterKey ? '' : filterKey
  handleSearch()
}

// Handle reset filters
const handleResetFilters = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = ''
    }
  })
  pagination.current = 1
  handleSearch()
}

// Handle save filter
const handleSaveFilter = () => {
  message.success('筛选条件已保存')
}



// Handle remove filter
const handleRemoveFilter = (filterKey) => {
  switch (filterKey) {
    case 'keyword':
      searchForm.keyword = ''
      break
    case 'location':
      searchForm.location = []
      break
    case 'industry':
      searchForm.industry = []
      break
    case 'companySize':
      searchForm.companySize = ''
      break
    case 'supplierType':
      searchForm.supplierType = ''
      break
  }
  handleSearch()
}

// Handle pagination change
const handlePaginationChange = (page, pageSize) => {
  pagination.current = page
  pagination.pageSize = pageSize
  handleSearch()
}

// Handle table change
const handleTableChange = (pag, _filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize

  if (sorter.field) {
    sortBy.value = sorter.field
  }

  handleSearch()
}

// Handle supplier selection
const handleSelectSupplier = (supplierId) => {
  const index = selectedSuppliers.value.indexOf(supplierId)
  if (index > -1) {
    selectedSuppliers.value.splice(index, 1)
  } else {
    selectedSuppliers.value.push(supplierId)
  }
}

// Handle view supplier detail
const handleViewSupplierDetail = (supplier) => {
  selectedSupplierDetail.value = supplier
  showSupplierDetailModal.value = true
}

// Handle initiate inquiry
const handleInitiateInquiry = (supplier) => {
  if (supplier.supplierType !== 'recommended') {
    message.warning('只有推荐品牌商才能发起询价')
    return
  }

  inquiryForm.supplierId = supplier.id
  inquiryForm.supplierName = supplier.companyName
  showInquiryModal.value = true
}

// Handle submit inquiry
const handleSubmitInquiry = async () => {
  if (!inquiryForm.partSpecifications || !inquiryForm.quantity) {
    message.error('请填写必填项')
    return
  }

  inquiryLoading.value = true
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))

    message.success('询价申请已提交，运营团队将尽快审核供应商')
    showInquiryModal.value = false

    // Reset form
    Object.keys(inquiryForm).forEach(key => {
      if (Array.isArray(inquiryForm[key])) {
        inquiryForm[key] = []
      } else {
        inquiryForm[key] = ''
      }
    })

  } catch (error) {
    message.error('提交失败，请重试')
  } finally {
    inquiryLoading.value = false
  }
}

// Handle cancel inquiry
const handleCancelInquiry = () => {
  showInquiryModal.value = false
  // Reset form
  Object.keys(inquiryForm).forEach(key => {
    if (Array.isArray(inquiryForm[key])) {
      inquiryForm[key] = []
    } else {
      inquiryForm[key] = ''
    }
  })
}

// Handle toggle bookmark
const handleToggleBookmark = async (supplier) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))

    // 更新本地状态
    supplier.isBookmarked = !supplier.isBookmarked

    if (supplier.isBookmarked) {
      message.success(`已收藏 ${supplier.companyName}`)
    } else {
      message.success(`已取消收藏 ${supplier.companyName}`)
    }
  } catch (error) {
    message.error('操作失败，请重试')
  }
}

// Handle logo error
const handleLogoError = (event) => {
  // 当logo加载失败时，隐藏img标签，显示默认logo
  event.target.style.display = 'none'
  const defaultLogo = event.target.nextElementSibling
  if (defaultLogo) {
    defaultLogo.style.display = 'flex'
  }
}



// Handle add to comparison
const handleAddToComparison = (supplier) => {
  if (comparisonList.value.length >= 3) {
    message.warning('最多只能对比3家供应商')
    return
  }

  if (comparisonList.value.find(s => s.id === supplier.id)) {
    message.warning('该供应商已在对比列表中')
    return
  }

  comparisonList.value.push(supplier)
  message.success(`已将 ${supplier.name} 添加到对比列表`)
}

// Handle remove from comparison
const handleRemoveFromComparison = (supplierId) => {
  const index = comparisonList.value.findIndex(s => s.id === supplierId)
  if (index > -1) {
    comparisonList.value.splice(index, 1)
  }
}

// Handle start comparison
const handleStartComparison = () => {
  if (selectedSuppliers.value.length < 2) {
    message.warning('至少需要选择2家供应商才能进行对比')
    return
  }

  if (selectedSuppliers.value.length > 3) {
    message.warning('最多只能对比3家供应商')
    return
  }

  // 获取选中的供应商数据
  const selectedData = supplierList.value.filter(supplier =>
    selectedSuppliers.value.includes(supplier.id)
  )

  // Prepare comparison data
  comparisonColumns.value = [
    { title: '对比项目', dataIndex: 'item', key: 'item', fixed: 'left', width: 120 },
    ...selectedData.map(supplier => ({
      title: supplier.companyName,
      dataIndex: supplier.id,
      key: supplier.id,
      width: 200
    }))
  ]

  comparisonData.value = [
    {
      key: 'location',
      item: '所在地区',
      ...selectedData.reduce((acc, supplier) => {
        acc[supplier.id] = supplier.region
        return acc
      }, {})
    },
    {
      key: 'industry',
      item: '行业类别',
      ...selectedData.reduce((acc, supplier) => {
        acc[supplier.id] = supplier.industry
        return acc
      }, {})
    },
    {
      key: 'establishmentYear',
      item: '成立时间',
      ...selectedData.reduce((acc, supplier) => {
        acc[supplier.id] = `${formatTimestamp(supplier.establishmentTime)}年`
        return acc
      }, {})
    },
    {
      key: 'registeredCapital',
      item: '注册资本',
      ...selectedData.reduce((acc, supplier) => {
        acc[supplier.id] = supplier.registeredCapital
        return acc
      }, {})
    },
    {
      key: 'employeeCount',
      item: '员工数量',
      ...selectedData.reduce((acc, supplier) => {
        acc[supplier.id] = `${supplier.socialSecurityCount}人`
        return acc
      }, {})
    },
    {
      key: 'supplierType',
      item: '供应商类型',
      ...selectedData.reduce((acc, supplier) => {
        acc[supplier.id] = supplier.supplierType === 'recommended' ? '推荐品牌商' : '普通供应商'
        return acc
      }, {})
    },
    {
      key: 'factoryLevel',
      item: '工厂等级',
      ...selectedData.reduce((acc, supplier) => {
        acc[supplier.id] = supplier.factoryLevel
        return acc
      }, {})
    }
  ]

  showComparisonModal.value = true
}



// Handle export suppliers
const handleExportSuppliers = () => {
  if (selectedSuppliers.value.length === 0) {
    message.warning('请先选择要导出的供应商')
    return
  }

  const selectedData = supplierList.value.filter(supplier =>
    selectedSuppliers.value.includes(supplier.id)
  )

  // 模拟导出功能
  message.success(`正在导出 ${selectedData.length} 家供应商数据...`)

  // 这里可以实现实际的导出逻辑
  console.log('导出数据:', selectedData)
}



// Utility functions
const formatCurrency = (amount) => {
  if (amount >= 10000) {
    return `${(amount / 10000).toFixed(1)}万元`
  }
  return `${amount.toLocaleString()}元`
}

const formatTimestamp = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.getFullYear()
}

const getInquiryStatusColor = (status) => {
  const colorMap = {
    'auditing': 'orange',
    'quoted': 'green',
    'rejected': 'red',
    'expired': 'gray'
  }
  return colorMap[status] || 'default'
}

const getInquiryStatusText = (status) => {
  const textMap = {
    'auditing': '审核中',
    'quoted': '已报价',
    'rejected': '已拒绝',
    'expired': '已过期'
  }
  return textMap[status] || status
}

// ==================== Lifecycle ====================

onMounted(() => {
  // Initialize supplier list with mock data
  supplierList.value = mockSupplierData.slice(0, pagination.pageSize)
  pagination.total = mockSupplierData.length

  // Initialize map if needed
  if (viewMode.value === 'map') {
    // Map initialization would go here
  }
})

// Watch for view mode changes
watch(viewMode, (newMode) => {
  if (newMode === 'map') {
    // Initialize map
    setTimeout(() => {
      // Map initialization code would go here
      mapStats.currentRegion = supplierList.value.length
      mapStats.recommended = supplierList.value.filter(s => s.supplierType === 'recommended').length
      mapStats.normal = supplierList.value.filter(s => s.supplierType === 'normal').length
    }, 100)
  }
})
</script>

<style lang="less" scoped>
.supplier-index-container {
  min-height: 100vh;

  .search-filter-section {
    margin-bottom: 16px;

    .search-card {
      .quick-search-bar {
        .search-input-group {
          display: flex;
          gap: 12px;
          margin-bottom: 16px;

          .main-search-input {
            flex: 1;
          }

          .filter-toggle-btn {
            min-width: 120px;
          }
        }

        .quick-filters {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;

          .quick-filter-tag {
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              transform: translateY(-1px);
            }

            i {
              margin-right: 4px;
            }
          }
        }
      }

      .advanced-filters-panel {
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;

        .filter-actions {
          margin-top: 16px;
          text-align: right;
        }
      }
    }
  }

  // Table operations styling similar to orderTable
  .table-operations {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
  }

  // Selection summary styling similar to orderTable
  .selection-summary {
    position: sticky;
    bottom: 0;
    margin-bottom: 16px;
    background-color: #fff4f0;
    padding: 12px 16px;
    border-radius: 4px;
    border: 1px solid #ffa39e;
    display: flex;
    justify-content: space-between;
    z-index: 1;

    .summary-content {
      display: flex;
      gap: 16px;
    }
  }

  .active-filters {
    margin-bottom: 16px;
    padding: 12px 16px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

    .active-filters-label {
      margin-right: 8px;
      font-size: 12px;
      color: #666;
    }
  }

  .main-content {
    display: flex;
    gap: 16px;

    .left-sidebar {
      width: 280px;
      flex-shrink: 0;

      .bookmarks-card,
      .comparison-card,
      .quick-actions-card {
        margin-bottom: 16px;

        :deep(.ant-card-body) {
          padding: 12px;
        }
      }

      .bookmark-folders {
        .bookmark-folder {
          display: flex;
          align-items: center;
          padding: 8px 12px;
          margin-bottom: 4px;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            background-color: #f5f5f5;
          }

          &.active {
            background-color: #f94c30;
            color: white;
          }

          i {
            margin-right: 8px;
            color: #f94c30;
          }

          &.active i {
            color: white;
          }

          span {
            flex: 1;
            font-size: 12px;
          }
        }
      }

      .comparison-items {
        margin-bottom: 12px;

        .comparison-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 6px 8px;
          margin-bottom: 4px;
          background-color: #f5f5f5;
          border-radius: 4px;

          .supplier-name {
            font-size: 12px;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }

    .right-content {
      flex: 1;

      .list-view {
        .supplier-table {
          background: white;
          border-radius: 6px;
          overflow: hidden;

          .supplier-name-cell {
            display: flex;
            align-items: center;
            gap: 8px;

            .bookmark-icon {
              color: #f94c30;
              font-size: 14px;
            }
          }

          :deep(.ant-table-thead > tr > th) {
            background-color: #fafafa;
            font-weight: 600;
          }
        }
      }

      .card-view {
        .supplier-card {
          border-radius: 12px;
          transition: all 0.3s;
          cursor: pointer;
          border: 1px solid #e8e8e8;
          overflow: hidden;

          &:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            transform: translateY(-4px);
            border-color: #d9d9d9;
          }

          &.selected {
            border-color: #f94c30;
            box-shadow: 0 0 0 2px rgba(249, 76, 48, 0.2);
          }

          :deep(.ant-card-head) {
            border-bottom: 1px solid #f0f0f0;
            padding: 12px 16px;
            min-height: auto;
          }

          :deep(.ant-card-body) {
            padding: 16px;
          }

          :deep(.ant-card-actions) {
            background: #fafafa;
            border-top: 1px solid #f0f0f0;
            padding: 8px 16px;

            li {
              margin: 0;
              padding: 0;

              .ant-btn {
                height: 36px;
                font-weight: 500;
              }
            }
          }

          .card-title {
            display: flex;
            align-items: center;
            gap: 8px;

            .supplier-name {
              font-weight: 600;
              color: #333;
              flex: 1;
              font-size: 14px;
            }

            .title-actions {
              display: flex;
              align-items: center;
              gap: 4px;

              .bookmark-btn {
                padding: 4px;
                border-radius: 4px;
                transition: all 0.3s;

                &:hover {
                  background-color: #f5f5f5;
                }

                &.bookmarked {
                  color: #f94c30;

                  &:hover {
                    background-color: rgba(249, 76, 48, 0.1);
                  }
                }

                i {
                  font-size: 14px;
                }
              }
            }
          }

          .card-content {
            .supplier-header {
              display: flex;
              gap: 12px;
              margin-bottom: 16px;
              padding-bottom: 12px;
              border-bottom: 1px solid #f0f0f0;

              .supplier-logo {
                width: 60px;
                height: 60px;
                flex-shrink: 0;
                border-radius: 8px;
                overflow: hidden;
                border: 1px solid #e8e8e8;

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }

                .default-logo {
                  width: 100%;
                  height: 100%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  color: white;

                  i {
                    font-size: 24px;
                  }
                }
              }

              .supplier-basic-info {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: space-between;

                .supplier-tags {
                  display: flex;
                  flex-wrap: wrap;
                  gap: 4px;
                  margin-bottom: 8px;

                  .recommended-tag {
                    font-weight: 500;
                    border: none;

                    i {
                      margin-right: 4px;
                    }
                  }
                }

                .supplier-location {
                  display: flex;
                  align-items: center;
                  color: #666;
                  font-size: 13px;

                  i {
                    margin-right: 6px;
                    color: #f94c30;
                  }
                }
              }
            }

            .supplier-details {
              margin-bottom: 16px;

              .detail-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                font-size: 13px;

                &:last-child {
                  margin-bottom: 0;
                }

                .detail-label {
                  color: #666;
                  font-weight: 400;
                }

                .detail-value {
                  color: #333;
                  font-weight: 500;
                  text-align: right;
                }
              }
            }

            .supplier-products {
              .products-label {
                font-size: 12px;
                color: #666;
                margin-bottom: 8px;
                font-weight: 500;
              }

              .products-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
                align-items: center;

                .more-products {
                  font-size: 11px;
                  color: #999;
                  background: #f5f5f5;
                  padding: 2px 6px;
                  border-radius: 10px;
                }
              }
            }
          }
        }

        .card-pagination {
          margin-top: 24px;
          text-align: center;
        }
      }

      .map-view {
        .map-container {
          position: relative;
          height: 600px;
          background: white;
          border-radius: 6px;
          overflow: hidden;

          .map-canvas {
            width: 100%;
            height: 100%;
            background: #f0f2f5;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 14px;

            &::before {
              content: '地图功能开发中...';
            }
          }

          .map-controls {
            position: absolute;
            top: 16px;
            right: 16px;
            display: flex;
            flex-direction: column;
            gap: 8px;

            .map-legend,
            .map-stats {
              min-width: 160px;

              .legend-item {
                display: flex;
                align-items: center;
                margin-bottom: 6px;
                font-size: 12px;

                .legend-dot {
                  width: 8px;
                  height: 8px;
                  border-radius: 50%;
                  margin-right: 8px;

                  &.audited {
                    background-color: #52c41a;
                  }

                  &.unaudited {
                    background-color: #faad14;
                  }
                }
              }
            }
          }

          .map-supplier-panel {
            position: absolute;
            bottom: 16px;
            left: 16px;
            width: 300px;

            .map-supplier-info {
              p {
                margin-bottom: 8px;
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }

  // Modal styles
  .supplier-detail-modal {
    .supplier-detail-content {
      .detail-actions {
        margin-top: 24px;
        text-align: center;
      }
    }
  }

  .inquiry-modal {
    :deep(.ant-form-item-label) {
      font-weight: 600;
    }
  }

  .inquiry-tracking-modal {
    :deep(.ant-table-thead > tr > th) {
      background-color: #fafafa;
      font-weight: 600;
    }
  }

  .comparison-modal {
    .comparison-content {
      :deep(.ant-table-thead > tr > th) {
        background-color: #fafafa;
        font-weight: 600;
        text-align: center;
      }

      :deep(.ant-table-tbody > tr > td) {
        text-align: center;
      }

      :deep(.ant-table-tbody > tr > td:first-child) {
        background-color: #fafafa;
        font-weight: 600;
        text-align: left;
      }
    }
  }
}

// Responsive design
@media (max-width: 1200px) {
  .supplier-index-container {
    .main-content {
      .left-sidebar {
        width: 240px;
      }
    }
  }
}

@media (max-width: 992px) {
  .supplier-index-container {
    padding: 16px;

    .main-content {
      flex-direction: column;

      .left-sidebar {
        width: 100%;
        order: 2;

        .bookmarks-card,
        .comparison-card,
        .quick-actions-card {
          display: inline-block;
          width: calc(33.333% - 8px);
          margin-right: 12px;
          margin-bottom: 0;
          vertical-align: top;

          &:last-child {
            margin-right: 0;
          }
        }
      }

      .right-content {
        order: 1;
      }
    }

    .table-operations {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    .selection-summary {
      flex-direction: column;
      gap: 12px;

      .summary-content {
        flex-wrap: wrap;
      }
    }
  }
}

@media (max-width: 768px) {
  .supplier-index-container {
    .search-filter-section {
      .search-card {
        .quick-search-bar {
          .search-input-group {
            flex-direction: column;

            .filter-toggle-btn {
              width: 100%;
            }
          }
        }
      }
    }

    .main-content {
      .left-sidebar {
        .bookmarks-card,
        .comparison-card,
        .quick-actions-card {
          width: 100%;
          margin-right: 0;
          margin-bottom: 12px;
        }
      }

      .right-content {
        .card-view {
          :deep(.ant-col) {
            &.ant-col-xs-24 {
              max-width: 100%;
              flex: 0 0 100%;
            }

            &.ant-col-sm-12 {
              max-width: 100%;
              flex: 0 0 100%;
            }
          }
        }
      }
    }
  }
}
</style>